// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

/// Widget to display emoji reactions on a message
class MessageReactionsWidget extends StatelessWidget {
  final VMessageReactions reactions;
  final Function(String emoji)? onReactionTap;
  final bool isMeSender;
  final String currentUserId;

  const MessageReactionsWidget({
    super.key,
    required this.reactions,
    this.onReactionTap,
    required this.isMeSender,
    required this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    if (reactions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Wrap(
        spacing: 4,
        runSpacing: 4,
        children: reactions.reactions.map((reaction) {
          return _buildReactionChip(context, reaction);
        }).toList(),
      ),
    );
  }

  Widget _buildReactionChip(BuildContext context, VMessageReaction reaction) {
    final hasUserReacted = reaction.userIds.contains(currentUserId);

    return GestureDetector(
      onTap: () => onReactionTap?.call(reaction.emoji),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: hasUserReacted
              ? (isMeSender
                  ? Colors.white.withOpacity(0.3)
                  : Colors.blue.withOpacity(0.2))
              : (isMeSender
                  ? Colors.white.withOpacity(0.1)
                  : Colors.grey.withOpacity(0.1)),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: hasUserReacted
                ? (isMeSender
                    ? Colors.white.withOpacity(0.5)
                    : Colors.blue.withOpacity(0.5))
                : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              reaction.emoji,
              style: const TextStyle(fontSize: 14),
            ),
            if (reaction.count > 1) ...[
              const SizedBox(width: 4),
              Text(
                '${reaction.count}',
                style: TextStyle(
                  fontSize: 12,
                  color: isMeSender ? Colors.white70 : Colors.black87,
                  fontWeight:
                      hasUserReacted ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Widget to show who reacted with what emoji (for detailed view)
class ReactionDetailsWidget extends StatelessWidget {
  final VMessageReactions reactions;
  final Function(String userId)? onUserTap;
  final String currentUserId;

  const ReactionDetailsWidget({
    super.key,
    required this.reactions,
    this.onUserTap,
    required this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    if (reactions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Reactions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...reactions.reactions.map((reaction) {
            return _buildReactionSection(context, reaction);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildReactionSection(
      BuildContext context, VMessageReaction reaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                reaction.emoji,
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
              Text(
                '${reaction.count} ${reaction.count == 1 ? 'person' : 'people'}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // Note: In a real implementation, you would fetch user names from user IDs
          // For now, we'll just show the user IDs
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: reaction.userIds.map((userId) {
              return GestureDetector(
                onTap: () => onUserTap?.call(userId),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    userId == currentUserId ? 'You' : 'User',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

/// Helper function to show reaction details in a bottom sheet
void showReactionDetails(
  BuildContext context,
  VMessageReactions reactions, {
  Function(String userId)? onUserTap,
  required String currentUserId,
}) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return ReactionDetailsWidget(
        reactions: reactions,
        onUserTap: onUserTap,
        currentUserId: currentUserId,
      );
    },
  );
}
